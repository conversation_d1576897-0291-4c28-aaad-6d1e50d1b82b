import { AuthStrategy } from "@/lib/auth-strategy";
import { LogLevel } from "@/lib/logger";
import type { PrimaryColor } from "@/styles/theme/types";

export interface ApiConfig {
	baseUrl: string;
	tenantId?: number | null;
	userId?: number | null;
}

export interface AppConfig {
	name: string;
	description: string;
	// Overriden by Settings Context.
	direction: "ltr" | "rtl";
	// Overriden by Settings Context.
	language: string;
	// Overriden by Settings Context.
	theme: "light" | "dark" | "system";
	themeColor: string;
	// Overriden by Settings Context.
	primaryColor: PrimaryColor;
	logLevel: keyof typeof LogLevel;
	authStrategy: keyof typeof AuthStrategy;
	// Redirect relative path for 401 unauthorized responses.
	notAuthorizedRedirectPath?: string;
	api: ApiConfig;
}

export const appConfig: AppConfig = {
	name: "AristoTelos 5",
	description: "",
	direction: "ltr",
	language: "en",
	theme: "light",
	themeColor: "#090a0b",
	primaryColor: "neonBlue",
	logLevel: (import.meta.env.VITE_LOG_LEVEL as keyof typeof LogLevel) || LogLevel.ALL,
	authStrategy: (import.meta.env.VITE_AUTH_STRATEGY as keyof typeof AuthStrategy) || AuthStrategy.NONE,
	notAuthorizedRedirectPath: import.meta.env.VITE_NOT_AUTHORIZED_REDIRECT_PATH,
	api: {
		baseUrl: import.meta.env.VITE_API_BASE_URL ?? window.location.origin,
		tenantId: import.meta.env.VITE_API_TENANT_ID ? parseInt(import.meta.env.VITE_API_TENANT_ID) : null,
		userId: import.meta.env.VITE_API_USER_ID ? parseInt(import.meta.env.VITE_API_USER_ID) : null,
	},
};
